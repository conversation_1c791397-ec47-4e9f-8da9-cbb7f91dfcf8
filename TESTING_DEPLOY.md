# Testing Deploy Workflow Guide

This guide explains how to safely test the updated deploy.yml workflow before using it in production.

## 🧪 Testing Methods

### 1. Quick Local Test (Recommended)

Run the comprehensive test script:

```bash
./test-deploy-workflow.sh
```

### 2. Local Testing with `act`

Test the workflow locally without touching AWS:

```bash
# Test the build job only (safe)
act -j build --env-file .env.test --container-architecture linux/amd64
```

### 3. Branch Testing

Create a test branch and modify the workflow:

```bash
git checkout -b test-deploy
# Modify deploy.yml to use a test environment
git push origin test-deploy
```

## 🔧 Safe Testing Modifications

### Option A: Test with Different Environment

Temporarily modify deploy.yml:

```yaml
env:
  AWS_REGION: ap-south-1
  S3_BUCKET: elasticbeanstalk-ap-south-1-${{ secrets.AWS_ACCOUNT_ID }}
  APP_NAME: diet-app-test  # Add -test suffix
  ENV_NAME: Diet-app-test-env  # Use test environment
```

### Option B: Add Dry Run Mode

Add a dry-run input to the workflow:

```yaml
on:
  push:
    tags:
      - dev
  workflow_dispatch:
    inputs:
      dry_run:
        description: 'Run in dry-run mode (no actual deployment)'
        required: false
        default: 'false'
        type: boolean
```

## 📋 Pre-Testing Checklist

- [ ] AWS credentials are properly set in GitHub secrets
- [ ] AWS_ACCOUNT_ID secret is added
- [ ] S3 bucket exists and is accessible
- [ ] Elastic Beanstalk application exists
- [ ] Test environment is available (optional)

## 🚀 Testing Steps

### Step 1: Quick Validation
```bash
./test-deploy-workflow.sh
```

### Step 2: Optional - Test with act (if you want to simulate the full workflow)
```bash
act -j build --env-file .env.test --container-architecture linux/amd64
```

### Step 3: Deploy to Production
```bash
git tag dev
git push origin dev
```

## 🛡️ Safety Measures

1. **Use test environments** when possible
2. **Test during low-traffic periods**
3. **Have rollback plan ready**
4. **Monitor deployment closely**
5. **Keep old deployment method available**

## 🔍 Troubleshooting

### Common Issues:

1. **AWS credentials not working**
   - Check secrets are properly set
   - Verify IAM permissions

2. **S3 bucket not found**
   - Check AWS_ACCOUNT_ID is correct
   - Verify bucket naming convention

3. **JAR file not found**
   - Check Maven build is successful
   - Verify JAR file path

4. **Elastic Beanstalk errors**
   - Check application and environment names
   - Verify EB permissions

## 📝 Test Results Template

```
## Test Results

### Local Build Test
- [ ] Maven build successful
- [ ] JAR file created
- [ ] File size: ___MB

### act Local Test
- [ ] Workflow syntax valid
- [ ] Build job completes
- [ ] No errors in logs

### AWS Connectivity Test
- [ ] AWS credentials work
- [ ] S3 bucket accessible
- [ ] EB application found
- [ ] EB environment accessible

### Full Deployment Test
- [ ] S3 upload successful
- [ ] EB version created
- [ ] Deployment successful
- [ ] Application accessible
```
