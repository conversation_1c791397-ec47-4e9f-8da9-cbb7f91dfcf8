package com.myfitnessjourney.backend_service.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.myfitnessjourney.backend_service.dto.FoodSearchResponseDto;
import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import com.myfitnessjourney.backend_service.service.impl.FoodSearchServiceImpl;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FoodSearchServiceTest {

    @Mock
    private FoodNutrientsRepository foodNutrientsRepository;

    @InjectMocks
    private FoodSearchServiceImpl foodSearchService;

    @BeforeEach
    void setUp() {
        // Setup is handled by @Mock and @InjectMocks annotations
    }

    @Test
    void testSearchFoods_WithValidQuery_ReturnsResults() {
        // Given
        String query = "apple";
        FoodNutrients mockFoodNutrients = new FoodNutrients();
        mockFoodNutrients.setCode("123456789");
        mockFoodNutrients.setProductName("Red Apple");
        mockFoodNutrients.setBrandTag("Fresh Fruits");
        mockFoodNutrients.setCategoryTags(Arrays.asList("fruits", "fresh", "organic"));
        mockFoodNutrients.setCountryTags(Arrays.asList("usa", "canada"));
        mockFoodNutrients.setImageUrl("http://example.com/apple.jpg");
        mockFoodNutrients.setEnergyKcal100g(new BigDecimal("52.0"));
        mockFoodNutrients.setFat100g(new BigDecimal("0.2"));
        mockFoodNutrients.setProteins100g(new BigDecimal("0.3"));
        mockFoodNutrients.setCarbohydrates100g(new BigDecimal("14.0"));
        mockFoodNutrients.setSugars100g(new BigDecimal("10.0"));
        mockFoodNutrients.setSalt100g(new BigDecimal("0.0"));
        mockFoodNutrients.setSodium100g(new BigDecimal("1.0"));

        List<FoodNutrients> mockResults = new ArrayList<>();
        mockResults.add(mockFoodNutrients);
        when(foodNutrientsRepository.searchFoodByText(query))
            .thenReturn(mockResults);

        // When
        List<FoodSearchResponseDto> results = foodSearchService.searchFoods(query);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());

        FoodSearchResponseDto result = results.get(0);
        assertEquals("123456789", result.getCode());
        assertEquals("Red Apple", result.getProductName());
        assertEquals("Fresh Fruits", result.getBrandTag());
        assertEquals(Arrays.asList("fruits", "fresh", "organic"), result.getCategoryTags());
        assertEquals(Arrays.asList("usa", "canada"), result.getCountryTags());
        assertEquals("http://example.com/apple.jpg", result.getImageUrl());
        assertEquals(52.0, result.getEnergyKcal100g());
        assertEquals(0.2, result.getFat100g());
        assertEquals(0.3, result.getProteins100g());
        assertEquals(14.0, result.getCarbohydrates100g());
        assertEquals(10.0, result.getSugars100g());
        assertEquals(0.0, result.getSalt100g());
        assertEquals(1.0, result.getSodium100g());

        verify(foodNutrientsRepository, times(1)).searchFoodByText(query);
    }

    @Test
    void testSearchFoods_WithEmptyQuery_ReturnsEmptyList() {
        // Given
        String query = "";

        // When
        List<FoodSearchResponseDto> results = foodSearchService.searchFoods(query);

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());

        verify(foodNutrientsRepository, never()).searchFoodByText(anyString());
    }

    @Test
    void testSearchFoods_WithNullQuery_ReturnsEmptyList() {
        // Given
        String query = null;

        // When
        List<FoodSearchResponseDto> results = foodSearchService.searchFoods(query);

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());

        verify(foodNutrientsRepository, never()).searchFoodByText(anyString());
    }

    @Test
    void testSearchFoods_WithWhitespaceQuery_ReturnsEmptyList() {
        // Given
        String query = "   ";

        // When
        List<FoodSearchResponseDto> results = foodSearchService.searchFoods(query);

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());

        verify(foodNutrientsRepository, never()).searchFoodByText(anyString());
    }
}
