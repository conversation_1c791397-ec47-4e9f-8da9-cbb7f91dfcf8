package com.myfitnessjourney.backend_service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import redis.embedded.RedisServer;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.net.ServerSocket;

/**
 * Test configuration for Redis that handles both local and CI environments.
 * - In CI (GitHub Actions): Uses TestContainers Redis
 * - In local development: Falls back to embedded Redis if no Redis is running
 */
@Configuration
@Profile("test")
@Slf4j
public class TestRedisConfig {

    private RedisServer embeddedRedisServer;
    private int redisPort;

    @PostConstruct
    public void startEmbeddedRedis() {
        try {
            // Check if Redis is already running (from TestContainers or local instance)
            if (isRedisRunning()) {
                log.info("Redis is already running, using existing instance");
                return;
            }

            // Start embedded Redis as fallback
            redisPort = findAvailablePort();
            embeddedRedisServer = new RedisServer(redisPort);
            embeddedRedisServer.start();
            log.info("Started embedded Redis server on port {}", redisPort);

        } catch (Exception e) {
            log.warn("Failed to start embedded Redis, tests will use TestContainers Redis", e);
        }
    }

    @PreDestroy
    public void stopEmbeddedRedis() {
        if (embeddedRedisServer != null && embeddedRedisServer.isActive()) {
            try {
                embeddedRedisServer.stop();
                log.info("Stopped embedded Redis server");
            } catch (Exception e) {
                log.warn("Error stopping embedded Redis server", e);
            }
        }
    }

    @Bean
    @Primary
    @ConditionalOnProperty(name = "spring.redis.host", matchIfMissing = true)
    public RedisConnectionFactory testRedisConnectionFactory() {
        // This will be overridden by TestContainers configuration in AbstractIntegrationTest
        // But provides a fallback for unit tests
        if (embeddedRedisServer != null && embeddedRedisServer.isActive()) {
            LettuceConnectionFactory factory = new LettuceConnectionFactory("localhost", redisPort);
            factory.afterPropertiesSet();
            return factory;
        }

        // Default fallback
        return new LettuceConnectionFactory();
    }

    private boolean isRedisRunning() {
        try {
            // Try to connect to default Redis port
            try (ServerSocket socket = new ServerSocket(6379)) {
                return false; // Port is available, Redis not running
            }
        } catch (IOException e) {
            return true; // Port is in use, likely Redis is running
        }
    }

    private int findAvailablePort() {
        try (ServerSocket socket = new ServerSocket(0)) {
            return socket.getLocalPort();
        } catch (IOException e) {
            throw new RuntimeException("Could not find available port for embedded Redis", e);
        }
    }
}
