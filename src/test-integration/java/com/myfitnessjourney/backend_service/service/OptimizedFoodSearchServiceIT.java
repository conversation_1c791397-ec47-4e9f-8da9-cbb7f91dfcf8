package com.myfitnessjourney.backend_service.service;

import com.myfitnessjourney.backend_service.AbstractIntegrationTest;
import com.myfitnessjourney.backend_service.dto.FoodSearchResponseDto;
import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import com.myfitnessjourney.backend_service.service.impl.OptimizedFoodSearchService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
class OptimizedFoodSearchServiceIT extends AbstractIntegrationTest {

    @Autowired
    private OptimizedFoodSearchService optimizedFoodSearchService;

    @Autowired
    private FoodNutrientsRepository foodNutrientsRepository;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @BeforeEach
    void setUp() {
        // Clear cache before each test
        Objects.requireNonNull(cacheManager.getCache("foodSearch")).clear();
        redisTemplate.getConnectionFactory().getConnection().flushAll();
        
        // Insert test data
        setupTestData();
    }

    private void setupTestData() {
        // Create test food items
        FoodNutrients chicken = createFoodNutrient(
            "12345", "Chicken Breast", "Fresh Poultry", 
            List.of("meat", "protein"), List.of("usa"), 
            new BigDecimal("165"), new BigDecimal("31"), new BigDecimal("3.6")
        );

        FoodNutrients chickenWings = createFoodNutrient(
            "12346", "Chicken Wings", "Wings Co", 
            List.of("meat", "protein"), List.of("usa"), 
            new BigDecimal("203"), new BigDecimal("30"), new BigDecimal("8.1")
        );

        FoodNutrients rice = createFoodNutrient(
            "12347", "White Rice", "Rice Brand", 
            List.of("grains", "carbs"), List.of("thailand"), 
            new BigDecimal("130"), new BigDecimal("2.7"), new BigDecimal("0.3")
        );

        FoodNutrients oats = createFoodNutrient(
            "12348", "Quaker Oats", "Quaker", 
            List.of("grains", "breakfast"), List.of("usa"), 
            new BigDecimal("389"), new BigDecimal("16.9"), new BigDecimal("6.9")
        );

        foodNutrientsRepository.saveAll(List.of(chicken, chickenWings, rice, oats));
    }

    private FoodNutrients createFoodNutrient(String code, String productName, String brandTag,
                                           List<String> categoryTags, List<String> countryTags,
                                           BigDecimal calories, BigDecimal protein, BigDecimal fat) {
        FoodNutrients food = new FoodNutrients();
        food.setCode(code);
        food.setProductName(productName);
        food.setBrandTag(brandTag);
        food.setCategoryTags(categoryTags);
        food.setCountryTags(countryTags);
        food.setEnergyKcal100g(calories);
        food.setProteins100g(protein);
        food.setFat100g(fat);
        food.setCarbohydrates100g(new BigDecimal("0"));
        food.setImageUrl("http://example.com/image.jpg");
        return food;
    }

    @Test
    void testSearchFoods_WithValidQuery_ReturnsResults() {
        // Given
        String query = "chicken";

        // When
        List<FoodSearchResponseDto> results = optimizedFoodSearchService.searchFoods(query);

        // Then
        assertThat(results).isNotEmpty();
        assertThat(results).hasSize(2);
        assertThat(results.get(0).getProductName()).containsIgnoringCase("chicken");
        assertThat(results.get(1).getProductName()).containsIgnoringCase("chicken");
    }

    @Test
    void testSearchFoods_WithPopularTerm_UsesFastPath() {
        // Given
        String popularQuery = "chicken"; // This is in POPULAR_TERMS

        // When
        long startTime = System.currentTimeMillis();
        List<FoodSearchResponseDto> results = optimizedFoodSearchService.searchFoods(popularQuery);
        long duration = System.currentTimeMillis() - startTime;

        // Then
        assertThat(results).isNotEmpty();
        assertThat(duration).isLessThan(100); // Should be very fast
    }

    @Test
    void testSearchFoods_WithCaching_SecondCallIsFaster() {
        // Given
        String query = "rice";

        // When - First call (cache miss)
        long startTime1 = System.currentTimeMillis();
        List<FoodSearchResponseDto> results1 = optimizedFoodSearchService.searchFoods(query);
        long duration1 = System.currentTimeMillis() - startTime1;

        // When - Second call (cache hit)
        long startTime2 = System.currentTimeMillis();
        List<FoodSearchResponseDto> results2 = optimizedFoodSearchService.searchFoods(query);
        long duration2 = System.currentTimeMillis() - startTime2;

        // Then
        assertThat(results1).isEqualTo(results2);
        assertThat(duration2).isLessThan(duration1); // Second call should be faster
        assertThat(duration2).isLessThan(10); // Cached call should be very fast
    }

    @Test
    void testSearchFoods_WithPrefixQuery_ReturnsRelevantResults() {
        // Given
        String prefixQuery = "chic"; // Should match chicken items

        // When
        List<FoodSearchResponseDto> results = optimizedFoodSearchService.searchFoods(prefixQuery);

        // Then
        assertThat(results).isNotEmpty();
        results.forEach(result -> 
            assertThat(result.getProductName().toLowerCase()).contains("chic"));
    }

    @Test
    void testSearchFoods_WithEmptyQuery_ReturnsEmptyList() {
        // Given
        String emptyQuery = "";

        // When
        List<FoodSearchResponseDto> results = optimizedFoodSearchService.searchFoods(emptyQuery);

        // Then
        assertThat(results).isEmpty();
    }

    @Test
    void testSearchFoods_WithNullQuery_ReturnsEmptyList() {
        // Given
        String nullQuery = null;

        // When
        List<FoodSearchResponseDto> results = optimizedFoodSearchService.searchFoods(nullQuery);

        // Then
        assertThat(results).isEmpty();
    }

    @Test
    void testSearchFoods_WithFuzzyMatching_FindsTypos() {
        // Given
        String typoQuery = "chiken"; // Typo for "chicken"

        // When
        List<FoodSearchResponseDto> results = optimizedFoodSearchService.searchFoods(typoQuery);

        // Then
        assertThat(results).isNotEmpty();
        // Should find chicken items despite the typo
        boolean foundChicken = results.stream()
            .anyMatch(result -> result.getProductName().toLowerCase().contains("chicken"));
        assertThat(foundChicken).isTrue();
    }

    @Test
    void testCacheWarmup_PopulatesCacheWithPopularTerms() {
        // Given
        // Clear cache first
        Objects.requireNonNull(cacheManager.getCache("foodSearch")).clear();

        // When
        optimizedFoodSearchService.warmUpCache();

        // Then
        // Check that popular terms are now cached
        List<FoodSearchResponseDto> cachedResults = optimizedFoodSearchService.searchFoods("chicken");
        assertThat(cachedResults).isNotEmpty();
        
        // Verify it's actually cached by checking Redis
        String cacheKey = "foodSearch::chicken";
        Boolean exists = redisTemplate.hasKey(cacheKey);
        assertThat(exists).isTrue();
    }

    @Test
    void testSearchPerformance_MeetsLatencyRequirements() {
        // Given
        String[] queries = {"chicken", "rice", "oats", "bread"};
        
        // When & Then
        for (String query : queries) {
            long startTime = System.currentTimeMillis();
            List<FoodSearchResponseDto> results = optimizedFoodSearchService.searchFoods(query);
            long duration = System.currentTimeMillis() - startTime;
            
            // First call might be slower due to database access
            assertThat(duration).isLessThan(100); // Should be under 100ms
            
            // Second call should be much faster (cached)
            startTime = System.currentTimeMillis();
            optimizedFoodSearchService.searchFoods(query);
            duration = System.currentTimeMillis() - startTime;
            
            assertThat(duration).isLessThan(10); // Cached call should be under 10ms
        }
    }

    @Test
    void testRedisConnection_IsWorking() {
        // Given
        String testKey = "test:connection";
        String testValue = "working";

        // When
        redisTemplate.opsForValue().set(testKey, testValue);
        String retrievedValue = (String) redisTemplate.opsForValue().get(testKey);

        // Then
        assertThat(retrievedValue).isEqualTo(testValue);
        
        // Cleanup
        redisTemplate.delete(testKey);
    }

    @Test
    void testSearchAnalytics_TracksSearchQueries() {
        // Given
        String query = "analytics_test";

        // When
        optimizedFoodSearchService.searchFoods(query);
        
        // Wait a bit for async analytics to complete
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then
        String analyticsKey = "search:analytics:" + query;
        Boolean exists = redisTemplate.hasKey(analyticsKey);
        assertThat(exists).isTrue();
        
        Object count = redisTemplate.opsForHash().get(analyticsKey, "count");
        assertThat(count).isNotNull();
        assertThat(Integer.parseInt(count.toString())).isGreaterThan(0);
    }
}
