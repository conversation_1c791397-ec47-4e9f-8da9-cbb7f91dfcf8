package com.myfitnessjourney.backend_service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;

@Slf4j
public abstract class AbstractIntegrationTest {

  // Static containers to be shared across all tests
  static PostgreSQLContainer<?> postgreSQLContainer;
  static GenericContainer<?> redisContainer;

  static {
    // Initialize PostgreSQL container
    postgreSQLContainer = new PostgreSQLContainer<>(DockerImageName.parse("postgres:latest"))
        .withDatabaseName("testdb")
        .withUsername("test")
        .withPassword("test");

    // Initialize Redis container
    redisContainer = new GenericContainer<>(DockerImageName.parse("redis:7-alpine"))
        .withExposedPorts(6379)
        .withCommand("redis-server", "--appendonly", "yes");

    // Start containers
    postgreSQLContainer.start();
    redisContainer.start();

    // Setup database schema
    createSchemaAndGrantPermissions(postgreSQLContainer);
  }

  @DynamicPropertySource
  static void configureProperties(DynamicPropertyRegistry registry) {
    // Database configuration
    String jdbcUrlWithSchema = postgreSQLContainer.getJdbcUrl() + "?currentSchema=dietapp";
    registry.add("spring.datasource.url", () -> jdbcUrlWithSchema);
    registry.add("spring.datasource.username", postgreSQLContainer::getUsername);
    registry.add("spring.datasource.password", postgreSQLContainer::getPassword);
    registry.add("spring.liquibase.change-log", () -> "classpath:db/changelog/db.changelog-master.yaml");

    // Redis configuration
    registry.add("spring.redis.host", redisContainer::getHost);
    registry.add("spring.redis.port", () -> redisContainer.getMappedPort(6379));
    registry.add("app.cache.redis.enabled", () -> "true");

    // Disable Redis password for tests
    registry.add("spring.redis.password", () -> "");
  }

  private static void createSchemaAndGrantPermissions(PostgreSQLContainer<?> postgreSQLContainer) {
    String jdbcUrl = postgreSQLContainer.getJdbcUrl();
    String username = postgreSQLContainer.getUsername();
    String password = postgreSQLContainer.getPassword();

    try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
        Statement statement = connection.createStatement()) {

      // Create the schema
      statement.execute("CREATE SCHEMA IF NOT EXISTS dietapp");

      // Grant permissions to the user
      statement.execute("GRANT ALL PRIVILEGES ON SCHEMA dietapp TO test");
      statement.execute("GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA dietapp TO test");
      statement.execute("GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA dietapp TO test");
    } catch (Exception e) {
      log.error("Error in DB setup", e);
    }
  }

}
