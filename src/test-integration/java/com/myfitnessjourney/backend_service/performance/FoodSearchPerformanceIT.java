package com.myfitnessjourney.backend_service.performance;

import com.myfitnessjourney.backend_service.AbstractIntegrationTest;
import com.myfitnessjourney.backend_service.dto.FoodSearchResponseDto;
import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import com.myfitnessjourney.backend_service.service.FoodSearchService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Performance tests to verify that the optimized food search meets latency requirements.
 * These tests simulate realistic load and measure response times.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@Slf4j
class FoodSearchPerformanceIT extends AbstractIntegrationTest {

    @Autowired
    private FoodSearchService foodSearchService;

    @Autowired
    private FoodNutrientsRepository foodNutrientsRepository;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String[] TEST_QUERIES = {
        "chicken", "rice", "bread", "milk", "egg", "apple", "banana", 
        "oats", "yogurt", "cheese", "salmon", "broccoli", "pasta", "quinoa"
    };

    @BeforeEach
    void setUp() {
        // Clear cache before each test
        Objects.requireNonNull(cacheManager.getCache("foodSearch")).clear();
        redisTemplate.getConnectionFactory().getConnection().flushAll();
        
        // Create a larger dataset for realistic performance testing
        setupLargeTestDataset();
    }

    private void setupLargeTestDataset() {
        List<FoodNutrients> foods = new ArrayList<>();
        
        // Create test data for each query term
        for (String query : TEST_QUERIES) {
            for (int i = 0; i < 10; i++) {
                foods.add(createFoodNutrient(
                    query + "_" + i,
                    query.substring(0, 1).toUpperCase() + query.substring(1) + " Product " + i,
                    "Brand " + i,
                    List.of(query, "test"),
                    new BigDecimal(100 + i),
                    new BigDecimal(10 + i),
                    new BigDecimal(5 + i)
                ));
            }
        }
        
        // Add some additional variety
        for (int i = 0; i < 50; i++) {
            foods.add(createFoodNutrient(
                "variety_" + i,
                "Random Food Item " + i,
                "Generic Brand",
                List.of("misc", "test"),
                new BigDecimal(150 + i),
                new BigDecimal(15),
                new BigDecimal(8)
            ));
        }

        foodNutrientsRepository.saveAll(foods);
        log.info("Created {} test food items", foods.size());
    }

    private FoodNutrients createFoodNutrient(String code, String productName, String brandTag,
                                           List<String> categoryTags, BigDecimal calories, 
                                           BigDecimal protein, BigDecimal fat) {
        FoodNutrients food = new FoodNutrients();
        food.setCode(code);
        food.setProductName(productName);
        food.setBrandTag(brandTag);
        food.setCategoryTags(categoryTags);
        food.setCountryTags(List.of("usa"));
        food.setEnergyKcal100g(calories);
        food.setProteins100g(protein);
        food.setFat100g(fat);
        food.setCarbohydrates100g(new BigDecimal("20"));
        food.setImageUrl("http://example.com/image.jpg");
        return food;
    }

    @Test
    void testColdStart_Performance() {
        // Test performance on first call (cold start - no cache)
        for (String query : TEST_QUERIES) {
            long startTime = System.currentTimeMillis();
            List<FoodSearchResponseDto> results = foodSearchService.searchFoods(query);
            long duration = System.currentTimeMillis() - startTime;

            log.info("Cold start search for '{}' took {}ms, found {} results", 
                    query, duration, results.size());

            // Cold start should still be reasonably fast (under 100ms)
            assertThat(duration).isLessThan(100);
            assertThat(results).isNotEmpty();
        }
    }

    @Test
    void testWarmCache_Performance() {
        // First, warm up the cache
        for (String query : TEST_QUERIES) {
            foodSearchService.searchFoods(query);
        }

        // Now test cached performance
        for (String query : TEST_QUERIES) {
            long startTime = System.currentTimeMillis();
            List<FoodSearchResponseDto> results = foodSearchService.searchFoods(query);
            long duration = System.currentTimeMillis() - startTime;

            log.info("Cached search for '{}' took {}ms, found {} results", 
                    query, duration, results.size());

            // Cached calls should be very fast (under 10ms)
            assertThat(duration).isLessThan(10);
            assertThat(results).isNotEmpty();
        }
    }

    @Test
    void testConcurrentRequests_Performance() throws InterruptedException {
        int numberOfThreads = 10;
        int requestsPerThread = 5;
        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch latch = new CountDownLatch(numberOfThreads * requestsPerThread);
        List<Long> durations = new CopyOnWriteArrayList<>();

        // Submit concurrent requests
        for (int i = 0; i < numberOfThreads; i++) {
            final int threadId = i;
            executor.submit(() -> {
                for (int j = 0; j < requestsPerThread; j++) {
                    String query = TEST_QUERIES[j % TEST_QUERIES.length];
                    
                    long startTime = System.currentTimeMillis();
                    List<FoodSearchResponseDto> results = foodSearchService.searchFoods(query);
                    long duration = System.currentTimeMillis() - startTime;
                    
                    durations.add(duration);
                    latch.countDown();
                    
                    log.debug("Thread {} request {} for '{}' took {}ms", 
                            threadId, j, query, duration);
                }
            });
        }

        // Wait for all requests to complete
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        assertThat(completed).isTrue();

        executor.shutdown();

        // Analyze results
        double averageDuration = durations.stream().mapToLong(Long::longValue).average().orElse(0);
        long maxDuration = durations.stream().mapToLong(Long::longValue).max().orElse(0);
        long minDuration = durations.stream().mapToLong(Long::longValue).min().orElse(0);

        log.info("Concurrent test results: avg={}ms, min={}ms, max={}ms, total requests={}", 
                averageDuration, minDuration, maxDuration, durations.size());

        // Performance requirements
        assertThat(averageDuration).isLessThan(50); // Average under 50ms
        assertThat(maxDuration).isLessThan(200);    // Max under 200ms
        assertThat(durations.size()).isEqualTo(numberOfThreads * requestsPerThread);
    }

    @Test
    void testThroughput_Performance() {
        // Test system throughput over a sustained period
        int durationSeconds = 10;
        long endTime = System.currentTimeMillis() + (durationSeconds * 1000);
        int requestCount = 0;
        List<Long> responseTimes = new ArrayList<>();

        while (System.currentTimeMillis() < endTime) {
            String query = TEST_QUERIES[requestCount % TEST_QUERIES.length];
            
            long startTime = System.currentTimeMillis();
            List<FoodSearchResponseDto> results = foodSearchService.searchFoods(query);
            long responseTime = System.currentTimeMillis() - startTime;
            
            responseTimes.add(responseTime);
            requestCount++;
            
            assertThat(results).isNotNull();
        }

        double requestsPerSecond = (double) requestCount / durationSeconds;
        double averageResponseTime = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0);

        log.info("Throughput test: {} requests in {}s = {:.2f} req/s, avg response time: {:.2f}ms", 
                requestCount, durationSeconds, requestsPerSecond, averageResponseTime);

        // Performance requirements
        assertThat(requestsPerSecond).isGreaterThan(50); // At least 50 requests per second
        assertThat(averageResponseTime).isLessThan(20);  // Average under 20ms
    }

    @Test
    void testMemoryUsage_UnderLoad() {
        // Monitor memory usage during heavy load
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        // Generate load
        for (int i = 0; i < 1000; i++) {
            String query = TEST_QUERIES[i % TEST_QUERIES.length];
            foodSearchService.searchFoods(query);
            
            if (i % 100 == 0) {
                System.gc(); // Suggest garbage collection
                long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                log.debug("Memory usage after {} requests: {} MB", 
                        i, (currentMemory - initialMemory) / 1024 / 1024);
            }
        }

        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;

        log.info("Memory increase after 1000 requests: {} MB", memoryIncrease / 1024 / 1024);

        // Memory increase should be reasonable (under 100MB)
        assertThat(memoryIncrease).isLessThan(100 * 1024 * 1024); // 100MB
    }

    @Test
    void testLatencyPercentiles() {
        // Test latency distribution
        int numberOfRequests = 100;
        List<Long> latencies = new ArrayList<>();

        for (int i = 0; i < numberOfRequests; i++) {
            String query = TEST_QUERIES[i % TEST_QUERIES.length];
            
            long startTime = System.currentTimeMillis();
            foodSearchService.searchFoods(query);
            long latency = System.currentTimeMillis() - startTime;
            
            latencies.add(latency);
        }

        latencies.sort(Long::compareTo);

        long p50 = latencies.get(numberOfRequests * 50 / 100);
        long p90 = latencies.get(numberOfRequests * 90 / 100);
        long p95 = latencies.get(numberOfRequests * 95 / 100);
        long p99 = latencies.get(numberOfRequests * 99 / 100);

        log.info("Latency percentiles: P50={}ms, P90={}ms, P95={}ms, P99={}ms", 
                p50, p90, p95, p99);

        // Latency requirements
        assertThat(p50).isLessThan(10);  // 50% under 10ms
        assertThat(p90).isLessThan(25);  // 90% under 25ms
        assertThat(p95).isLessThan(50);  // 95% under 50ms
        assertThat(p99).isLessThan(100); // 99% under 100ms
    }
}
