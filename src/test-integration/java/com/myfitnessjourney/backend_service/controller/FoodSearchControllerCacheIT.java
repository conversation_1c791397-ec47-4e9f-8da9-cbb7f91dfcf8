package com.myfitnessjourney.backend_service.controller;

import com.myfitnessjourney.backend_service.AbstractIntegrationTest;
import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
class FoodSearchControllerCacheIT extends AbstractIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private FoodNutrientsRepository foodNutrientsRepository;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @BeforeEach
    void setUp() {
        // Clear cache before each test
        Objects.requireNonNull(cacheManager.getCache("foodSearch")).clear();
        redisTemplate.getConnectionFactory().getConnection().flushAll();

        // Insert test data
        setupTestData();
    }

    private void setupTestData() {
        List<FoodNutrients> testFoods = List.of(
            createFoodNutrient("1", "Grilled Chicken Breast", "Premium Poultry"),
            createFoodNutrient("2", "Chicken Thighs", "Farm Fresh"),
            createFoodNutrient("3", "Jasmine Rice", "Asian Best"),
            createFoodNutrient("4", "Brown Rice", "Healthy Grains"),
            createFoodNutrient("5", "Steel Cut Oats", "Morning Fuel")
        );

        foodNutrientsRepository.saveAll(testFoods);
    }

    private FoodNutrients createFoodNutrient(String code, String productName, String brandTag) {
        FoodNutrients food = new FoodNutrients();
        food.setCode(code);
        food.setProductName(productName);
        food.setBrandTag(brandTag);
        food.setCategoryTags(List.of("test"));
        food.setCountryTags(List.of("usa"));
        food.setEnergyKcal100g(new BigDecimal("150"));
        food.setProteins100g(new BigDecimal("20"));
        food.setFat100g(new BigDecimal("5"));
        food.setCarbohydrates100g(new BigDecimal("10"));
        food.setImageUrl("http://example.com/image.jpg");
        return food;
    }

    @Test
    void testSearchFoods_FirstCall_PopulatesCache() throws Exception {
        // Given
        String query = "chicken";
        String expectedCacheKey = "foodSearch::" + query;

        // Verify cache is empty initially
        assertThat(redisTemplate.hasKey(expectedCacheKey)).isFalse();

        // When
        MvcResult result = mockMvc.perform(get("/api/v1/foods/search")
                .param("q", query)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2)) // Should find 2 chicken items
                .andReturn();

        // Then
        // Verify cache was populated
        assertThat(redisTemplate.hasKey(expectedCacheKey)).isTrue();

        // Verify response contains expected data
        String responseContent = result.getResponse().getContentAsString();
        assertThat(responseContent).contains("Chicken");
    }

    @Test
    void testSearchFoods_SecondCall_UsesCache() throws Exception {
        // Given
        String query = "rice";

        // First call to populate cache
        long startTime1 = System.currentTimeMillis();
        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", query)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        long duration1 = System.currentTimeMillis() - startTime1;

        // When - Second call (should use cache)
        long startTime2 = System.currentTimeMillis();
        MvcResult result = mockMvc.perform(get("/api/v1/foods/search")
                .param("q", query)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2)) // Should find 2 rice items
                .andReturn();
        long duration2 = System.currentTimeMillis() - startTime2;

        // Then
        // Second call should be faster (cached)
        assertThat(duration2).isLessThan(duration1);

        // Verify cache key exists
        String expectedCacheKey = "foodSearch::" + query;
        assertThat(redisTemplate.hasKey(expectedCacheKey)).isTrue();
    }

    @Test
    void testSearchFoods_CaseInsensitive_SharesCache() throws Exception {
        // Given
        String lowerQuery = "chicken";
        String upperQuery = "CHICKEN";
        String mixedQuery = "ChIcKeN";

        // When - Make calls with different cases
        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", lowerQuery))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", upperQuery))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", mixedQuery))
                .andExpect(status().isOk());

        // Then
        // Should only have one cache entry (normalized to lowercase)
        Set<String> cacheKeys = redisTemplate.keys("foodSearch::*chicken*");
        assertThat(cacheKeys).hasSize(1);
        assertThat(cacheKeys.iterator().next()).isEqualTo("foodSearch::chicken");
    }

    @Test
    void testSearchFoods_EmptyQuery_DoesNotCache() throws Exception {
        // Given
        String emptyQuery = "";

        // When
        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", emptyQuery)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));

        // Then
        // Should not create cache entry for empty query
        Set<String> cacheKeys = redisTemplate.keys("foodSearch::*");
        assertThat(cacheKeys).isEmpty();
    }

    @Test
    void testSearchFoods_WithSpecialCharacters_HandlesGracefully() throws Exception {
        // Given
        String specialQuery = "chicken@#$%";

        // When
        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", specialQuery)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());

        // Then
        // Should handle special characters without crashing
        String expectedCacheKey = "foodSearch::" + specialQuery.toLowerCase().trim();
        // Cache key should exist (even if results are empty)
        assertThat(redisTemplate.hasKey(expectedCacheKey)).isTrue();
    }

    @Test
    void testSearchFoods_Performance_MeetsLatencyRequirements() throws Exception {
        // Given
        String[] queries = {"chicken", "rice", "oats"};

        for (String query : queries) {
            // First call (database + cache population)
            long startTime1 = System.currentTimeMillis();
            mockMvc.perform(get("/api/v1/foods/search")
                    .param("q", query))
                    .andExpect(status().isOk());
            long duration1 = System.currentTimeMillis() - startTime1;

            // Should be reasonably fast even for first call
            assertThat(duration1).isLessThan(1000); // Under 1 second

            // Second call (cached)
            long startTime2 = System.currentTimeMillis();
            mockMvc.perform(get("/api/v1/foods/search")
                    .param("q", query))
                    .andExpect(status().isOk());
            long duration2 = System.currentTimeMillis() - startTime2;

            // Cached call should be very fast
            assertThat(duration2).isLessThan(100); // Under 100ms
        }
    }

    @Test
    void testSearchFoods_ConcurrentRequests_HandleCorrectly() throws Exception {
        // Given
        String query = "chicken";
        int numberOfThreads = 5;
        Thread[] threads = new Thread[numberOfThreads];

        // When - Make concurrent requests
        for (int i = 0; i < numberOfThreads; i++) {
            threads[i] = new Thread(() -> {
                try {
                    mockMvc.perform(get("/api/v1/foods/search")
                            .param("q", query))
                            .andExpect(status().isOk());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }

        // Then
        // Should have one cache entry
        String expectedCacheKey = "foodSearch::" + query;
        assertThat(redisTemplate.hasKey(expectedCacheKey)).isTrue();

        // Final request should be fast (cached)
        long startTime = System.currentTimeMillis();
        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", query))
                .andExpect(status().isOk());
        long duration = System.currentTimeMillis() - startTime;

        assertThat(duration).isLessThan(50); // Should be very fast
    }

    @Test
    void testSearchFoods_LongQuery_HandlesCorrectly() throws Exception {
        // Given
        String longQuery = "a".repeat(100); // 100 character query

        // When
        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", longQuery)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());

        // Then
        // Should handle long queries without issues
        String expectedCacheKey = "foodSearch::" + longQuery.toLowerCase().trim();
        assertThat(redisTemplate.hasKey(expectedCacheKey)).isTrue();
    }

    @Test
    void testSearchFoods_CacheExpiration_IsSetCorrectly() throws Exception {
        // Given
        String query = "oats";

        // When
        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", query))
                .andExpect(status().isOk());

        // Then
        String expectedCacheKey = "foodSearch::" + query;
        assertThat(redisTemplate.hasKey(expectedCacheKey)).isTrue();

        // Check TTL is set (should be 30 minutes = 1800 seconds)
        Long ttl = redisTemplate.getExpire(expectedCacheKey);
        assertThat(ttl).isGreaterThan(0);
        assertThat(ttl).isLessThanOrEqualTo(1800);
    }
}
