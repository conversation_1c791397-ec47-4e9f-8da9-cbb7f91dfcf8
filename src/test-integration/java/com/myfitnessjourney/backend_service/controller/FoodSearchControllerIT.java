package com.myfitnessjourney.backend_service.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.myfitnessjourney.backend_service.AbstractIntegrationTest;
import com.myfitnessjourney.backend_service.service.FoodSearchService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
class FoodSearchControllerIT extends AbstractIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FoodSearchService foodSearchService;

    @Test
    void testSearchFoods_WithValidQuery_ReturnsOk() throws Exception {
        // Given
        String query = "apple";
        when(foodSearchService.searchFoods(query)).thenReturn(List.of());

        // When & Then
        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", query)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testSearchFoods_WithEmptyQuery_ReturnsOk() throws Exception {
        // Given
        String query = "";
        when(foodSearchService.searchFoods(query)).thenReturn(List.of());

        // When & Then
        mockMvc.perform(get("/api/v1/foods/search")
                .param("q", query)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testSearchFoods_WithoutQueryParam_ReturnsInternalServerError() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/foods/search")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }
}
