spring:
  datasource:
    url: ***************************************
    username: test
    password: test
  jpa:
    properties:
      hibernate:
        default_schema: dietapp
        flushMode: COMMIT
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.yaml
    default-schema: dietapp
    enabled: true
  redis:
    # These will be overridden by TestContainers in AbstractIntegrationTest
    host: localhost
    port: 6379
    password: ""
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
aws:
  s3:
    bucket-name: diet-app-images
    region: dummy-region
  credentials:
    access-key: dummy-access-key
    secret-key: dummy-secret-key

app:
  cache:
    redis:
      enabled: true

logging:
  level:
    liquibase: DEBUG
    com.myfitnessjourney.backend_service.service: DEBUG
    org.springframework.cache: DEBUG
