package com.myfitnessjourney.backend_service.repository;

import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FoodNutrientsRepository extends JpaRepository<FoodNutrients, Long> {

    @Query(value = """
        SELECT *,
               CASE
                   WHEN search_vector @@ plainto_tsquery('english', :query)
                   THEN ts_rank_cd(search_vector, plainto_tsquery('english', :query))
                   ELSE 0.0
               END AS rank_score,
               similarity(product_name, :query) AS fuzzy_score
        FROM food_nutrients
        WHERE search_vector @@ plainto_tsquery('english', :query)
           OR similarity(product_name, :query) > 0.3
        ORDER BY
            rank_score DESC,
            fuzzy_score DESC
        LIMIT 20;
        """, nativeQuery = true)
    List<FoodNutrients> searchFoodByText(@Param("query") String query);

    @Query(value = """
        SELECT * FROM popular_food_nutrients
        WHERE search_vector @@ plainto_tsquery('english', :query)
           OR similarity(product_name, :query) > 0.3
        ORDER BY base_rank DESC, similarity(product_name, :query) DESC
        LIMIT 20;
        """, nativeQuery = true)
    List<FoodNutrients> searchPopularFoods(@Param("query") String query);

    @Query(value = """
        SELECT *,
               ts_rank_cd(search_vector, plainto_tsquery('english', :query)) AS rank_score,
               similarity(product_name, :query) AS fuzzy_score
        FROM food_nutrients
        WHERE product_name ILIKE :prefix || '%'
           OR search_vector @@ plainto_tsquery('english', :query)
        ORDER BY
            CASE WHEN product_name ILIKE :prefix || '%' THEN 1 ELSE 2 END,
            rank_score DESC,
            fuzzy_score DESC
        LIMIT 20;
        """, nativeQuery = true)
    List<FoodNutrients> searchByPrefix(@Param("query") String query, @Param("prefix") String prefix);
}
