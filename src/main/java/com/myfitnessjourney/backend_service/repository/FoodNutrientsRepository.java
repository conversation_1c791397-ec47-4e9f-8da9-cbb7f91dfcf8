package com.myfitnessjourney.backend_service.repository;

import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FoodNutrientsRepository extends JpaRepository<FoodNutrients, Long> {

    @Query(value = """
        SELECT *,
               CASE
                   WHEN search_vector @@ plainto_tsquery('english', :query)
                   THEN ts_rank_cd(search_vector, plainto_tsquery('english', :query))
                   ELSE 0.0
               END AS rank_score,
               similarity(product_name, :query) AS fuzzy_score
        FROM food_nutrients
        WHERE search_vector @@ plainto_tsquery('english', :query)
           OR similarity(product_name, :query) > 0.3
        ORDER BY
            rank_score DESC,
            fuzzy_score DESC
        LIMIT 20;
        """, nativeQuery = true)
    List<FoodNutrients> searchFoodByText(@Param("query") String query);
}
