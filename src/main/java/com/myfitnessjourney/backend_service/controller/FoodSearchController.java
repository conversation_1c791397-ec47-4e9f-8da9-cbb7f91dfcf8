package com.myfitnessjourney.backend_service.controller;

import com.myfitnessjourney.backend_service.dto.FoodSearchResponseDto;
import com.myfitnessjourney.backend_service.service.FoodSearchService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/foods")
@RequiredArgsConstructor
@Slf4j
public class FoodSearchController {

    private final FoodSearchService foodSearchService;

    @GetMapping("/search")
    public ResponseEntity<List<FoodSearchResponseDto>> searchFoods(
            @RequestParam("q") String query) {
        
        log.info("Food search request received with query: {}", query);
        
        List<FoodSearchResponseDto> results = foodSearchService.searchFoods(query);
        
        log.info("Found {} food items for query: {}", results.size(), query);
        
        return ResponseEntity.ok(results);
    }
}
