package com.myfitnessjourney.backend_service.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FoodSearchResponseDto {
    private String code;
    private String productName;
    private String brandTag;
    private List<String> categoryTags;
    private List<String> countryTags;
    private String imageUrl;
    private Double energyKcal100g;
    private Double fat100g;
    private Double proteins100g;
    private Double carbohydrates100g;
    private Double sugars100g;
    private Double salt100g;
    private Double sodium100g;
}
