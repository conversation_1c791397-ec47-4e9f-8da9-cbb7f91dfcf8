package com.myfitnessjourney.backend_service.service;

import com.myfitnessjourney.backend_service.service.impl.OptimizedFoodSearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
@EnableAsync
public class CacheWarmupService {

    private final OptimizedFoodSearchService optimizedFoodSearchService;

    @EventListener(ApplicationReadyEvent.class)
    @Async
    public void warmUpCacheOnStartup() {
        log.info("Starting cache warm-up process...");
        try {
            optimizedFoodSearchService.warmUpCache();
            log.info("Cache warm-up completed successfully");
        } catch (Exception e) {
            log.error("Cache warm-up failed", e);
        }
    }

    // Refresh cache every 6 hours
    @Scheduled(fixedRate = 21600000) // 6 hours in milliseconds
    @Async
    public void refreshCache() {
        log.info("Refreshing cache...");
        try {
            optimizedFoodSearchService.warmUpCache();
            log.info("Cache refresh completed");
        } catch (Exception e) {
            log.error("Cache refresh failed", e);
        }
    }
}
