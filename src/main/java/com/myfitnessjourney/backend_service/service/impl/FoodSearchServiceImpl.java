package com.myfitnessjourney.backend_service.service.impl;

import com.myfitnessjourney.backend_service.dto.FoodSearchResponseDto;
import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import com.myfitnessjourney.backend_service.service.FoodSearchService;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FoodSearchServiceImpl implements FoodSearchService {

    private final FoodNutrientsRepository foodNutrientsRepository;

    @Override
    public List<FoodSearchResponseDto> searchFoods(String query) {
        if (query == null || query.trim().isEmpty()) {
            return List.of();
        }

        log.debug("Searching for foods with query: {}", query);

        List<FoodNutrients> results = foodNutrientsRepository.searchFoodByText(query.trim());

        return results.stream()
                .map(this::mapToFoodSearchResponse)
                .collect(Collectors.toList());
    }

    private FoodSearchResponseDto mapToFoodSearchResponse(FoodNutrients foodNutrients) {
        return FoodSearchResponseDto.builder()
                .code(foodNutrients.getCode())
                .productName(foodNutrients.getProductName())
                .brandTag(foodNutrients.getBrandTag())
                .categoryTags(foodNutrients.getCategoryTags())
                .countryTags(foodNutrients.getCountryTags())
                .imageUrl(foodNutrients.getImageUrl())
                .energyKcal100g(getDouble(foodNutrients.getEnergyKcal100g()))
                .fat100g(getDouble(foodNutrients.getFat100g()))
                .proteins100g(getDouble(foodNutrients.getProteins100g()))
                .carbohydrates100g(getDouble(foodNutrients.getCarbohydrates100g()))
                .sugars100g(getDouble(foodNutrients.getSugars100g()))
                .salt100g(getDouble(foodNutrients.getSalt100g()))
                .sodium100g(getDouble(foodNutrients.getSodium100g()))
                .build();
    }

    private Double getDouble(BigDecimal value) {
        return value != null ? value.doubleValue() : null;
    }
}
