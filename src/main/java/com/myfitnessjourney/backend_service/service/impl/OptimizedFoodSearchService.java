package com.myfitnessjourney.backend_service.service.impl;

import com.myfitnessjourney.backend_service.dto.FoodSearchResponseDto;
import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import com.myfitnessjourney.backend_service.service.FoodSearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Primary
@RequiredArgsConstructor
@Slf4j
public class OptimizedFoodSearchService implements FoodSearchService {

    private final FoodNutrientsRepository foodNutrientsRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    // Popular search terms that get pre-cached
    private static final Set<String> POPULAR_TERMS = Set.of(
        "chicken", "rice", "bread", "milk", "egg", "apple", "banana",
        "oats", "yogurt", "cheese", "salmon", "broccoli", "pasta", "quinoa"
    );

    @Override
    @Cacheable(value = "foodSearch", key = "#query.toLowerCase().trim()",
               unless = "#query == null or #query.trim().isEmpty()")
    public List<FoodSearchResponseDto> searchFoods(String query) {
        if (query == null || query.trim().isEmpty()) {
            return List.of();
        }

        String normalizedQuery = query.toLowerCase().trim();
        long startTime = System.currentTimeMillis();

        try {
            // Strategy 1: Check if it's a popular term (fastest path)
            if (POPULAR_TERMS.contains(normalizedQuery)) {
                log.debug("Using optimized path for popular term: {}", normalizedQuery);
                return searchPopularTerm(normalizedQuery);
            }

            // Strategy 2: Check for exact prefix matches (second fastest)
            if (normalizedQuery.length() >= 3) {
                List<FoodSearchResponseDto> prefixResults = searchByPrefix(normalizedQuery);
                if (!prefixResults.isEmpty()) {
                    log.debug("Found {} prefix matches for: {}", prefixResults.size(), normalizedQuery);
                    return prefixResults;
                }
            }

            // Strategy 3: Full search (fallback)
            log.debug("Using full search for query: {}", normalizedQuery);
            return performFullSearch(normalizedQuery);

        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.debug("Search completed in {}ms for query: {}", duration, normalizedQuery);

            // Async analytics tracking
            CompletableFuture.runAsync(() -> trackSearchAnalytics(normalizedQuery, duration));
        }
    }

    private List<FoodSearchResponseDto> searchPopularTerm(String query) {
        // Use materialized view for popular terms
        List<FoodNutrients> results = foodNutrientsRepository.searchPopularFoods(query);
        return mapToResponseDtos(results);
    }

    private List<FoodSearchResponseDto> searchByPrefix(String query) {
        // Fast prefix search using specialized index
        List<FoodNutrients> results = foodNutrientsRepository.searchByPrefix(query, query);
        return mapToResponseDtos(results);
    }

    private List<FoodSearchResponseDto> performFullSearch(String query) {
        List<FoodNutrients> results = foodNutrientsRepository.searchFoodByText(query);
        return mapToResponseDtos(results);
    }

    private List<FoodSearchResponseDto> mapToResponseDtos(List<FoodNutrients> results) {
        return results.stream()
                .map(this::mapToFoodSearchResponse)
                .collect(Collectors.toList());
    }

    private FoodSearchResponseDto mapToFoodSearchResponse(FoodNutrients foodNutrients) {
        return FoodSearchResponseDto.builder()
                .code(foodNutrients.getCode())
                .productName(foodNutrients.getProductName())
                .brandTag(foodNutrients.getBrandTag())
                .categoryTags(foodNutrients.getCategoryTags())
                .countryTags(foodNutrients.getCountryTags())
                .imageUrl(foodNutrients.getImageUrl())
                .energyKcal100g(getDouble(foodNutrients.getEnergyKcal100g()))
                .fat100g(getDouble(foodNutrients.getFat100g()))
                .proteins100g(getDouble(foodNutrients.getProteins100g()))
                .carbohydrates100g(getDouble(foodNutrients.getCarbohydrates100g()))
                .sugars100g(getDouble(foodNutrients.getSugars100g()))
                .salt100g(getDouble(foodNutrients.getSalt100g()))
                .sodium100g(getDouble(foodNutrients.getSodium100g()))
                .build();
    }

    private Double getDouble(BigDecimal value) {
        return value != null ? value.doubleValue() : null;
    }

    private void trackSearchAnalytics(String query, long duration) {
        try {
            String key = "search:analytics:" + query;
            redisTemplate.opsForHash().increment(key, "count", 1);
            redisTemplate.opsForHash().put(key, "last_duration", duration);
            redisTemplate.expire(key, Duration.ofDays(30));
        } catch (Exception e) {
            log.warn("Failed to track search analytics for query: {}", query, e);
        }
    }

    // Warm up cache with popular searches
    public void warmUpCache() {
        log.info("Warming up cache with popular search terms...");
        POPULAR_TERMS.parallelStream().forEach(term -> {
            try {
                searchFoods(term);
                log.debug("Warmed up cache for: {}", term);
            } catch (Exception e) {
                log.warn("Failed to warm up cache for term: {}", term, e);
            }
        });
        log.info("Cache warm-up completed");
    }
}
