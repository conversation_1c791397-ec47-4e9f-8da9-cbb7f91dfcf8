package com.myfitnessjourney.backend_service.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "food_nutrients")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FoodNutrients {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code", unique = true, nullable = false)
    private String code;

    @Column(name = "product_name", nullable = false)
    private String productName;

    @Column(name = "brand_tag")
    private String brandTag;

    @Column(name = "category_tags", columnDefinition = "text[]")
    private List<String> categoryTags;

    @Column(name = "country_tags", columnDefinition = "text[]")
    private List<String> countryTags;

    @Column(name = "image_url")
    private String imageUrl;

    @Column(name = "energy_kcal_100g")
    private BigDecimal energyKcal100g;

    @Column(name = "fat_100g")
    private BigDecimal fat100g;

    @Column(name = "proteins_100g")
    private BigDecimal proteins100g;

    @Column(name = "carbohydrates_100g")
    private BigDecimal carbohydrates100g;

    @Column(name = "sugars_100g")
    private BigDecimal sugars100g;

    @Column(name = "salt_100g")
    private BigDecimal salt100g;

    @Column(name = "sodium_100g")
    private BigDecimal sodium100g;

    @Column(name = "search_vector", columnDefinition = "tsvector", insertable = false, updatable = false)
    private String searchVector;
}
