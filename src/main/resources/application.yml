server:
  port: 8080
spring:
  application.name: backend-service
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://dev-yg535pmx2jriftrc.us.auth0.com/
          audience: https://dev-yg535pmx2jriftrc.us.auth0.com/api/v2/
  doc:
    api-docs:
      path: /v3/api-docs  # Default is /v3/api-docs
      use-root-path: true
    swagger-ui:
      path: /swagger-ui.html  # Default is /swagger-ui.html
      urls:
        - name: 'My API'
          url: /v3/api-docs
    http:
      enabled: true  # Enable HTTP/HTTPS
  datasource:
    url: ${DATABASE_URL}
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: none # Change to 'validate', 'update', 'create', or 'create-drop' as needed
    show-sql: false
    properties:
      hibernate:
        format_sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  liquibase:
    enabled: false # Disable Liquibasespring:
    change-log: classpath:db/changelog/db.changelog-master-old.yaml
management:
  endpoints:
    web:
      exposure:
        include: mappings
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

aws:
  s3:
    bucket-name: diet-app-images
    region: ${AWS_REGION}
  credentials:
    access-key: ${AWS_ACCESS_KEY}
    secret-key: ${AWS_SECRET_KEY}
  lambda:
    urls:
      food-image: https://r2pea55t2a.execute-api.ap-south-1.amazonaws.com/default/foodapireqdevapp
      text-nutrition: https://3w5feog47d.execute-api.ap-south-1.amazonaws.com/default/apitextnutrition
      audio-diet: https://xg03ivq0n0.execute-api.ap-south-1.amazonaws.com/default/audiodietapi

app:
  cache:
    redis:
      enabled: ${REDIS_CACHE_ENABLED:true}

logging:
  level:
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.springframework.web.filter: INFO
    org.springframework.web.servlet: INFO
    org.springframework.web.servlet.mvc: INFO