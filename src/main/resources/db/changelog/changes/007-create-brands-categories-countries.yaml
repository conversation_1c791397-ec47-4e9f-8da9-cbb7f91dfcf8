databaseChangeLog:
  - changeSet:
      id: 007-create-brands-table
      author: fdeshmukh
      changes:
        - createTable:
            tableName: brands
            columns:
              - column:
                  name: brand_tag
                  type: TEXT
                  constraints:
                    primaryKey: true
              - column:
                  name: brand_name
                  type: TEXT

  - changeSet:
      id: 007-create-categories-table
      author: fdeshmukh
      changes:
        - createTable:
            tableName: categories
            columns:
              - column:
                  name: category_tag
                  type: TEXT
                  constraints:
                    primaryKey: true
              - column:
                  name: category_name
                  type: TEXT

  - changeSet:
      id: 007-create-countries-table
      author: fdeshmukh
      changes:
        - createTable:
            tableName: countries
            columns:
              - column:
                  name: country_tag
                  type: TEXT
                  constraints:
                    primaryKey: true
              - column:
                  name: country_name
                  type: TEXT