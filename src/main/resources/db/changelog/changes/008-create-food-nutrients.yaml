databaseChangeLog:
  - changeSet:
      id: 008-create-food-nutrients
      author: fdeshmukh
      changes:
        - createTable:
            tableName: food_nutrients
            columns:
              - column:
                  name: id
                  type: SERIAL
                  constraints:
                    primaryKey: true
              - column:
                  name: code
                  type: TEXT
                  constraints:
                    unique: true
                    nullable: false
              - column:
                  name: product_name
                  type: TEXT
                  constraints:
                    nullable: false
              - column:
                  name: brand_tag
                  type: TEXT
              - column:
                  name: category_tags
                  type: TEXT[]
              - column:
                  name: country_tags
                  type: TEXT[]
              - column:
                  name: image_url
                  type: TEXT
              - column:
                  name: energy_kcal_100g
                  type: NUMERIC
              - column:
                  name: fat_100g
                  type: NUMERIC
              - column:
                  name: proteins_100g
                  type: NUMERIC
              - column:
                  name: carbohydrates_100g
                  type: NUMERIC
              - column:
                  name: sugars_100g
                  type: NUMERIC
              - column:
                  name: salt_100g
                  type: NUMERIC
              - column:
                  name: sodium_100g
                  type: NUMERIC
              - column:
                  name: search_vector
                  type: TSVECTOR