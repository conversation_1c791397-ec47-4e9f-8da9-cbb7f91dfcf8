databaseChangeLog:
  - changeSet:
      id: 010-optimize-food-nutrients-performance
      author: fdeshmukh
      changes:
        # Create a materialized view for popular searches
        - sql:
            sql: |
              CREATE MATERIALIZED VIEW IF NOT EXISTS popular_food_nutrients AS
              SELECT *,
                     ts_rank_cd(search_vector, plainto_tsquery('english', 'popular')) AS base_rank
              FROM food_nutrients
              WHERE search_vector @@ plainto_tsquery('english', 'chicken')
                 OR search_vector @@ plainto_tsquery('english', 'rice')
                 OR search_vector @@ plainto_tsquery('english', 'bread')
                 OR search_vector @@ plainto_tsquery('english', 'milk')
                 OR search_vector @@ plainto_tsquery('english', 'egg')
                 OR search_vector @@ plainto_tsquery('english', 'apple')
                 OR search_vector @@ plainto_tsquery('english', 'banana')
                 OR search_vector @@ plainto_tsquery('english', 'oats')
                 OR search_vector @@ plainto_tsquery('english', 'yogurt')
                 OR search_vector @@ plainto_tsquery('english', 'cheese')
              ORDER BY base_rank DESC;

        # Create index on materialized view
        - sql:
            sql: |
              CREATE UNIQUE INDEX IF NOT EXISTS idx_popular_food_nutrients_id 
              ON popular_food_nutrients (id);
              
              CREATE INDEX IF NOT EXISTS idx_popular_food_nutrients_search 
              ON popular_food_nutrients USING GIN (search_vector);
              
              CREATE INDEX IF NOT EXISTS idx_popular_food_nutrients_name_trgm 
              ON popular_food_nutrients USING GIN (product_name gin_trgm_ops);

        # Add additional performance indexes
        - sql:
            sql: |
              -- Composite index for common search patterns
              CREATE INDEX IF NOT EXISTS idx_food_nutrients_composite 
              ON food_nutrients (product_name, brand_tag) 
              WHERE energy_kcal_100g IS NOT NULL;
              
              -- Index for category-based searches
              CREATE INDEX IF NOT EXISTS idx_food_nutrients_category_search 
              ON food_nutrients USING GIN (category_tags, search_vector);
              
              -- Partial index for high-quality entries
              CREATE INDEX IF NOT EXISTS idx_food_nutrients_quality 
              ON food_nutrients (product_name, energy_kcal_100g) 
              WHERE energy_kcal_100g > 0 AND proteins_100g IS NOT NULL;

        # Create function to refresh materialized view
        - sql:
            sql: |
              CREATE OR REPLACE FUNCTION refresh_popular_food_nutrients()
              RETURNS void AS $$
              BEGIN
                REFRESH MATERIALIZED VIEW CONCURRENTLY popular_food_nutrients;
              END;
              $$ LANGUAGE plpgsql;

  - changeSet:
      id: 010-create-search-analytics
      author: fdeshmukh
      changes:
        # Create table to track search patterns for optimization
        - createTable:
            tableName: food_search_analytics
            columns:
              - column:
                  name: id
                  type: SERIAL
                  constraints:
                    primaryKey: true
              - column:
                  name: search_query
                  type: TEXT
                  constraints:
                    nullable: false
              - column:
                  name: search_count
                  type: INTEGER
                  defaultValue: 1
              - column:
                  name: avg_response_time_ms
                  type: INTEGER
              - column:
                  name: last_searched
                  type: TIMESTAMP WITHOUT TIME ZONE
                  defaultValueComputed: now()
              - column:
                  name: created_at
                  type: TIMESTAMP WITHOUT TIME ZONE
                  defaultValueComputed: now()

        # Create indexes for analytics table
        - sql:
            sql: |
              CREATE INDEX idx_food_search_analytics_query 
              ON food_search_analytics (search_query);
              
              CREATE INDEX idx_food_search_analytics_count 
              ON food_search_analytics (search_count DESC);
              
              CREATE INDEX idx_food_search_analytics_last_searched 
              ON food_search_analytics (last_searched DESC);
