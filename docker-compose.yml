version: '3.8'

services:
  database:
    image: postgres:15
    container_name: dietapp_postgres
    environment:
      POSTGRES_USER: liquibase_user
      POSTGRES_PASSWORD: 'my_password'
      POSTGRES_DB: dietapp_db
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./db/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    container_name: dietapp_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

volumes:
  db_data:
  redis_data:
