#!/bin/bash

# Simple AWS CLI deployment script
set -e

echo "🚀 Building and deploying with AWS CLI..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="diet-app"
ENV_NAME="Diet-app-env-1"
REGION="ap-south-1"
JAR_FILE="target/diet-app-backend-0.0.1-SNAPSHOT.jar"
VERSION_LABEL="manual-$(date +%Y%m%d-%H%M%S)"

# Get AWS account ID
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
S3_BUCKET="elasticbeanstalk-${REGION}-${ACCOUNT_ID}"

echo -e "${YELLOW}📦 Building application...${NC}"
./mvnw clean package

# Check if JAR was created
if [ ! -f "${JAR_FILE}" ]; then
    echo -e "${RED}❌ JAR file not found: ${JAR_FILE}${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build completed successfully${NC}"

# Upload JAR to S3 first
echo -e "${YELLOW}⬆️ Uploading JAR to S3...${NC}"
S3_KEY="${APP_NAME}/${VERSION_LABEL}.jar"

# Check if S3 bucket exists, create if it doesn't
if ! aws s3 ls "s3://${S3_BUCKET}" 2>/dev/null; then
    echo -e "${YELLOW}📦 Creating S3 bucket: ${S3_BUCKET}${NC}"
    aws s3 mb "s3://${S3_BUCKET}" --region ${REGION}
fi

aws s3 cp ${JAR_FILE} "s3://${S3_BUCKET}/${S3_KEY}"

# Create application version
echo -e "${YELLOW}📦 Creating application version: ${VERSION_LABEL}${NC}"
aws elasticbeanstalk create-application-version \
    --application-name ${APP_NAME} \
    --version-label ${VERSION_LABEL} \
    --source-bundle S3Bucket="${S3_BUCKET}",S3Key="${S3_KEY}" \
    --region ${REGION}

# Deploy to environment
echo -e "${YELLOW}🚀 Deploying to environment: ${ENV_NAME}${NC}"
aws elasticbeanstalk update-environment \
    --application-name ${APP_NAME} \
    --environment-name ${ENV_NAME} \
    --version-label ${VERSION_LABEL} \
    --region ${REGION}

echo -e "${GREEN}✅ Deployment initiated successfully!${NC}"
echo -e "${GREEN}🌐 Check AWS Console for deployment status:${NC}"
echo -e "${GREEN}   https://${REGION}.console.aws.amazon.com/elasticbeanstalk/home?region=${REGION}#/environment/dashboard?environmentId=${ENV_NAME}${NC}"
