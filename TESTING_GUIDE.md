# Testing Guide for Food Search Optimization

## Overview
This guide covers the comprehensive test suite for the optimized food search API, including integration tests, performance tests, and caching validation.

## 🧪 Test Structure

### **Integration Tests**
Located in `src/test-integration/java/`

#### **1. OptimizedFoodSearchServiceIT**
- **Purpose**: Tests the core optimized search functionality
- **Coverage**: Multi-strategy search, caching, fuzzy matching
- **Key Tests**:
  - Popular terms fast path
  - Prefix matching optimization
  - Cache hit/miss scenarios
  - Fuzzy search for typos
  - Performance requirements

#### **2. CacheWarmupServiceIT**
- **Purpose**: Tests cache warming and refresh functionality
- **Coverage**: Startup cache population, scheduled refresh
- **Key Tests**:
  - Cache population on startup
  - Refresh mechanism
  - Error handling
  - Cache key formatting

#### **3. FoodSearchControllerCacheIT**
- **Purpose**: End-to-end API testing with caching
- **Coverage**: HTTP endpoints, cache behavior, concurrent requests
- **Key Tests**:
  - Cache population via API calls
  - Response time improvements
  - Concurrent request handling
  - Cache key normalization

#### **4. FoodSearchPerformanceIT**
- **Purpose**: Performance benchmarking and latency validation
- **Coverage**: Throughput, latency percentiles, memory usage
- **Key Tests**:
  - Cold start performance (< 100ms)
  - Cached query performance (< 10ms)
  - Concurrent load testing
  - Throughput requirements (> 50 req/s)
  - Latency percentiles (P99 < 100ms)

## 🐳 TestContainers Setup

### **AbstractIntegrationTest**
- **PostgreSQL Container**: Provides isolated database for each test run
- **Redis Container**: Provides Redis instance for caching tests
- **Dynamic Configuration**: Automatically configures connection properties
- **Shared Containers**: Optimizes test execution time

### **Benefits**:
- ✅ **Isolation**: Each test run uses fresh containers
- ✅ **Consistency**: Same environment locally and in CI
- ✅ **No Dependencies**: No need for local Redis/PostgreSQL
- ✅ **Parallel Execution**: Tests can run concurrently

## 🚀 Running Tests

### **Local Development**

#### **Prerequisites**
```bash
# Ensure Docker is running
docker --version

# Java 21 is required
java --version
```

#### **Run All Tests**
```bash
# Unit tests only
./mvnw test

# Integration tests only
./mvnw verify -Dspring.profiles.active=test

# All tests including performance
./mvnw verify -Dspring.profiles.active=test -Dtest="**/*IT,**/*PerformanceIT"
```

#### **Run Specific Test Categories**
```bash
# Cache-related tests only
./mvnw test -Dtest="**/*Cache*IT"

# Performance tests only
./mvnw test -Dtest="**/*PerformanceIT"

# Service layer tests only
./mvnw test -Dtest="**/service/**/*IT"
```

### **With Local Redis (Optional)**
If you have Redis running locally, tests will detect and use it:
```bash
# Start local Redis
docker run -d -p 6379:6379 redis:7-alpine

# Run tests (will use local Redis)
./mvnw verify -Dspring.profiles.active=test
```

### **GitHub Actions CI**
Tests automatically run on:
- Push to `main` or `develop` branches
- Pull requests
- Uses TestContainers for complete isolation
- Includes performance benchmarking
- Generates test reports and coverage

## 📊 Performance Requirements

### **Latency Targets**
| Scenario | Target | Test Validation |
|----------|--------|-----------------|
| Cold Start | < 100ms | `testColdStart_Performance()` |
| Cached Query | < 10ms | `testWarmCache_Performance()` |
| Popular Terms | < 5ms | `testSearchFoods_WithPopularTerm_UsesFastPath()` |
| P50 Latency | < 10ms | `testLatencyPercentiles()` |
| P90 Latency | < 25ms | `testLatencyPercentiles()` |
| P99 Latency | < 100ms | `testLatencyPercentiles()` |

### **Throughput Targets**
| Metric | Target | Test Validation |
|--------|--------|-----------------|
| Requests/Second | > 50 | `testThroughput_Performance()` |
| Concurrent Users | 10+ | `testConcurrentRequests_Performance()` |
| Memory Usage | < 100MB increase | `testMemoryUsage_UnderLoad()` |

## 🔧 Test Configuration

### **Environment Variables**
```bash
# Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_CACHE_ENABLED=true

# Database configuration (TestContainers will override)
DATABASE_URL=***************************************
DB_USER=test
DB_PASSWORD=test

# TestContainers configuration
TESTCONTAINERS_RYUK_DISABLED=false
```

### **Test Profiles**
- **`test`**: Main test profile with TestContainers
- **`local`**: For local development with external services

### **Application-test.yml Configuration**
```yaml
spring:
  redis:
    host: localhost  # Overridden by TestContainers
    port: 6379
    timeout: 2000ms
  
app:
  cache:
    redis:
      enabled: true

logging:
  level:
    com.myfitnessjourney.backend_service.service: DEBUG
    org.springframework.cache: DEBUG
```

## 🐛 Troubleshooting

### **Common Issues**

#### **TestContainers Not Starting**
```bash
# Check Docker is running
docker info

# Check available ports
netstat -tulpn | grep :6379
netstat -tulpn | grep :5432

# Clean up containers
docker system prune -f
```

#### **Redis Connection Issues**
```bash
# Check Redis container logs
docker logs $(docker ps -q --filter ancestor=redis:7-alpine)

# Test Redis connectivity
docker run --rm redis:7-alpine redis-cli -h host.docker.internal ping
```

#### **Performance Tests Failing**
- Ensure sufficient system resources
- Close other applications during testing
- Check if running in CI environment (different thresholds)

#### **Cache Tests Failing**
```bash
# Clear Redis cache manually
docker exec -it $(docker ps -q --filter ancestor=redis:7-alpine) redis-cli FLUSHALL

# Check cache configuration
grep -r "cache" src/test-integration/resources/
```

### **Debug Mode**
```bash
# Run tests with debug logging
./mvnw test -Dspring.profiles.active=test -Dlogging.level.root=DEBUG

# Run specific test with debug
./mvnw test -Dtest="OptimizedFoodSearchServiceIT#testSearchFoods_WithCaching_SecondCallIsFaster" -Dspring.profiles.active=test
```

## 📈 Test Reports

### **Generated Reports**
- **Surefire Reports**: `target/surefire-reports/`
- **Failsafe Reports**: `target/failsafe-reports/`
- **Coverage Reports**: `target/site/jacoco/`

### **CI Artifacts**
- Test results uploaded to GitHub Actions
- Performance metrics in job summaries
- Coverage reports as artifacts

## 🎯 Best Practices

### **Writing New Tests**
1. **Extend AbstractIntegrationTest** for database/Redis access
2. **Use @Transactional** for test isolation
3. **Clear cache in @BeforeEach** for consistent state
4. **Test both success and failure scenarios**
5. **Include performance assertions** where relevant

### **Performance Testing**
1. **Warm up** before measuring performance
2. **Use realistic data volumes** (100+ records)
3. **Test concurrent scenarios** for real-world conditions
4. **Monitor memory usage** during load tests
5. **Set reasonable thresholds** based on requirements

### **Cache Testing**
1. **Verify cache population** after operations
2. **Test cache key normalization** (case sensitivity)
3. **Validate TTL settings** for cache entries
4. **Test cache miss/hit scenarios** explicitly
5. **Handle Redis unavailability** gracefully

## 🔄 Continuous Integration

The test suite is designed to:
- ✅ **Run reliably** in any environment
- ✅ **Provide fast feedback** on performance regressions
- ✅ **Validate optimization goals** automatically
- ✅ **Generate actionable reports** for developers
- ✅ **Scale with the application** as it grows
