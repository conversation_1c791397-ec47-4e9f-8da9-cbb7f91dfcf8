name: Cleanup S3 Artifacts

on:
  workflow_dispatch:  # Manual trigger
  schedule:
    - cron: '0 2 * * 0'  # Runs weekly on Sunday at 2am

env:
  AWS_REGION: ap-south-1
  APP_NAME: diet-app

jobs:
  cleanup-s3-artifacts:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Get AWS Account ID and setup S3 bucket
        id: aws-info
        run: |
          ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
          S3_BUCKET="elasticbeanstalk-${{ env.AWS_REGION }}-${ACCOUNT_ID}"
          echo "s3-bucket=$S3_BUCKET" >> $GITHUB_OUTPUT
          echo "AWS Account ID: $ACCOUNT_ID"
          echo "S3 Bucket: $S3_BUCKET"

      - name: List current artifacts
        run: |
          S3_BUCKET="${{ steps.aws-info.outputs.s3-bucket }}"
          echo "📋 Listing current artifacts in S3..."
          aws s3 ls "s3://$S3_BUCKET/${{ env.APP_NAME }}/" --recursive --human-readable

      - name: Clean up old artifacts (keep last 5)
        run: |
          S3_BUCKET="${{ steps.aws-info.outputs.s3-bucket }}"
          echo "🧹 Cleaning up old artifacts (keeping last 5)..."

          # Get list of objects sorted by date (newest first)
          OBJECTS=$(aws s3api list-objects-v2 \
            --bucket "$S3_BUCKET" \
            --prefix "${{ env.APP_NAME }}/" \
            --query 'Contents[?contains(Key, `.jar`)] | sort_by(@, &LastModified) | reverse(@) | [5:].Key' \
            --output text)

          if [ -z "$OBJECTS" ]; then
            echo "ℹ️ No old artifacts to clean up (less than 5 artifacts found)"
          else
            echo "🗑️ Deleting old artifacts:"
            for obj in $OBJECTS; do
              echo "  - $obj"
              aws s3 rm "s3://$S3_BUCKET/$obj"
            done
            echo "✅ Cleanup completed"
          fi

      - name: List remaining artifacts
        run: |
          S3_BUCKET="${{ steps.aws-info.outputs.s3-bucket }}"
          echo "📋 Remaining artifacts after cleanup:"
          aws s3 ls "s3://$S3_BUCKET/${{ env.APP_NAME }}/" --recursive --human-readable
