name: Remove old artifacts

on:
  workflow_dispatch:  # Manual trigger
  schedule:
    - cron: '0 1 * * *'  # Runs daily at 1am

jobs:
  remove-old-artifacts:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    # Required permissions for private repos
    permissions:
      actions: write

    steps:
      - name: Remove old artifacts
        uses: c-hive/gha-remove-artifacts@v1.4.0  # Pin to specific version
        with:
          age: '1 week'  # Remove artifacts older than 2 weeks
          # Optional: Uncomment below to keep only latest N artifacts instead
          skip-recent: 2  # Keep the 5 most recent artifacts
          # Optional: Uncomment below to protect release artifacts
          # skip-tags: true  # Keep artifacts from tagged releases
