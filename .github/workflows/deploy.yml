name: Deployment

on:
  push:
    tags:
      - dev  # Trigger only when pushing a tag 'dev'

env:
  AWS_REGION: ap-south-1
  APP_NAME: diet-app
  ENV_NAME: Diet-app-env-1

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    outputs:
      s3-key: ${{ steps.upload.outputs.s3-key }}
      version-label: ${{ steps.version.outputs.version-label }}
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin' # See 'Supported distributions' for available options
          java-version: '21'

      - name: Build with Maven
        run: ./mvnw clean verify package --file pom.xml

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Generate version label
        id: version
        run: |
          VERSION_LABEL="github-${{ github.sha }}-$(date +%Y%m%d-%H%M%S)"
          echo "version-label=$VERSION_LABEL" >> $GITHUB_OUTPUT
          echo "Generated version label: $VERSION_LABEL"

      - name: Upload JAR to S3
        id: upload
        run: |
          JAR_FILE="target/diet-app-backend-0.0.1-SNAPSHOT.jar"
          S3_KEY="${{ env.APP_NAME }}/${{ steps.version.outputs.version-label }}.jar"

          # Check if JAR exists
          if [ ! -f "$JAR_FILE" ]; then
            echo "❌ JAR file not found: $JAR_FILE"
            exit 1
          fi

          # Upload to S3
          echo "⬆️ Uploading JAR to S3: s3://${{ env.S3_BUCKET }}/$S3_KEY"
          aws s3 cp "$JAR_FILE" "s3://${{ env.S3_BUCKET }}/$S3_KEY"

          # Output for next job
          echo "s3-key=$S3_KEY" >> $GITHUB_OUTPUT
          echo "✅ Upload completed successfully"

  deploy:
    needs: build
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Create Elastic Beanstalk application version
        run: |
          echo "📦 Creating application version: ${{ needs.build.outputs.version-label }}"
          aws elasticbeanstalk create-application-version \
            --application-name ${{ env.APP_NAME }} \
            --version-label ${{ needs.build.outputs.version-label }} \
            --source-bundle S3Bucket="${{ env.S3_BUCKET }}",S3Key="${{ needs.build.outputs.s3-key }}" \
            --region ${{ env.AWS_REGION }}

      - name: Deploy to Elastic Beanstalk
        run: |
          echo "🚀 Deploying to environment: ${{ env.ENV_NAME }}"
          aws elasticbeanstalk update-environment \
            --application-name ${{ env.APP_NAME }} \
            --environment-name ${{ env.ENV_NAME }} \
            --version-label ${{ needs.build.outputs.version-label }} \
            --region ${{ env.AWS_REGION }}

          echo "✅ Deployment initiated successfully"

      - name: Wait for deployment completion
        run: |
          echo "⏳ Waiting for deployment to complete..."
          aws elasticbeanstalk wait environment-updated \
            --application-name ${{ env.APP_NAME }} \
            --environment-name ${{ env.ENV_NAME }} \
            --region ${{ env.AWS_REGION }}

          echo "🎉 Deployment completed successfully!"