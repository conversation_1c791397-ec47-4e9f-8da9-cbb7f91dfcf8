#!/bin/bash

# Test script for deploy workflow
set -e

echo "🧪 Testing Deploy Workflow Components"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test 1: Check if JAR file exists
echo -e "${BLUE}1. Testing JAR file creation...${NC}"
JAR_FILE="target/diet-app-backend-0.0.1-SNAPSHOT.jar"
if [ -f "$JAR_FILE" ]; then
    echo -e "${GREEN}✅ JAR file exists: $JAR_FILE${NC}"
    echo -e "   File size: $(du -h "$JAR_FILE" | cut -f1)"
else
    echo -e "${RED}❌ JAR file not found. Running build...${NC}"
    ./mvnw clean package
    if [ -f "$JAR_FILE" ]; then
        echo -e "${GREEN}✅ JAR file created successfully${NC}"
    else
        echo -e "${RED}❌ Failed to create JAR file${NC}"
        exit 1
    fi
fi

# Test 2: Generate version label (simulate workflow)
echo -e "${BLUE}2. Testing version label generation...${NC}"
VERSION_LABEL="test-$(git rev-parse --short HEAD)-$(date +%Y%m%d-%H%M%S)"
echo -e "${GREEN}✅ Generated version label: $VERSION_LABEL${NC}"

# Test 3: Simulate S3 key generation
echo -e "${BLUE}3. Testing S3 key generation...${NC}"
APP_NAME="diet-app"
S3_KEY="${APP_NAME}/${VERSION_LABEL}.jar"
echo -e "${GREEN}✅ Generated S3 key: $S3_KEY${NC}"

# Test 4: Check AWS CLI availability (if installed)
echo -e "${BLUE}4. Testing AWS CLI availability...${NC}"
if command -v aws &> /dev/null; then
    echo -e "${GREEN}✅ AWS CLI is installed${NC}"

    # Test AWS credentials (if configured)
    if aws sts get-caller-identity &> /dev/null; then
        echo -e "${GREEN}✅ AWS credentials are configured${NC}"
        ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
        echo -e "   Account ID: $ACCOUNT_ID"

        # Test S3 bucket access
        S3_BUCKET="elasticbeanstalk-ap-south-1-${ACCOUNT_ID}"
        echo -e "${BLUE}5. Testing S3 bucket access...${NC}"
        if aws s3 ls "s3://${S3_BUCKET}/" &> /dev/null; then
            echo -e "${GREEN}✅ S3 bucket is accessible: $S3_BUCKET${NC}"
        else
            echo -e "${YELLOW}⚠️ S3 bucket not accessible or doesn't exist: $S3_BUCKET${NC}"
        fi

        # Test Elastic Beanstalk application
        echo -e "${BLUE}6. Testing Elastic Beanstalk application...${NC}"
        if aws elasticbeanstalk describe-applications --application-names $APP_NAME &> /dev/null; then
            echo -e "${GREEN}✅ Elastic Beanstalk application exists: $APP_NAME${NC}"
        else
            echo -e "${YELLOW}⚠️ Elastic Beanstalk application not found: $APP_NAME${NC}"
        fi

    else
        echo -e "${YELLOW}⚠️ AWS credentials not configured${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ AWS CLI not installed${NC}"
fi

# Test 5: Workflow file validation
echo -e "${BLUE}7. Testing workflow file syntax...${NC}"
if command -v act &> /dev/null; then
    if act -l &> /dev/null; then
        echo -e "${GREEN}✅ Workflow syntax is valid${NC}"
    else
        echo -e "${RED}❌ Workflow syntax has errors${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️ act not installed, skipping workflow validation${NC}"
fi

echo ""
echo -e "${GREEN}🎉 All tests completed!${NC}"
echo ""
echo -e "${BLUE}Summary:${NC}"
echo -e "- JAR file: ${GREEN}✅${NC}"
echo -e "- Version label: ${GREEN}✅${NC}"
echo -e "- S3 key generation: ${GREEN}✅${NC}"
echo -e "- Workflow syntax: ${GREEN}✅${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Add AWS_ACCOUNT_ID secret to GitHub repository"
echo "2. Test with a non-production environment first"
echo "3. Push a 'dev' tag to trigger the workflow"
echo ""
echo -e "${BLUE}To test manually:${NC}"
echo "git tag dev-test && git push origin dev-test"
